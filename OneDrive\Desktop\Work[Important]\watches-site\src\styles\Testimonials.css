.testimonials {
  padding: 120px 0;
  background: #c2c2c3;
  color: #232738;
  position: relative;
  overflow: hidden;
}

.testimonials-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.testimonials-header {
  text-align: center;
  margin-bottom: 80px;
}

.testimonials-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: #232738;
  text-transform: uppercase;
  letter-spacing: -0.02em;
}

.testimonials-subtitle {
  font-size: 1.2rem;
  color: #232738;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  opacity: 0.8;
}

.testimonials-content {
  margin-bottom: 80px;
}

.testimonial-main {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.testimonial-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 50px;
  max-width: 800px;
  width: 100%;
  position: relative;
  transition: all 0.5s ease;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.testimonial-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.customer-avatar {
  position: relative;
}

.avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b35, #2c5aa0);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.verified-badge {
  position: absolute;
  bottom: -5px;
  right: -5px;
  width: 24px;
  height: 24px;
  background: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
  font-weight: 700;
  border: 2px solid #1a1a1a;
}

.customer-details {
  flex: 1;
}

.customer-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 5px;
}

.customer-title {
  font-size: 1rem;
  color: #ff6b35;
  font-weight: 600;
  margin-bottom: 3px;
}

.customer-location {
  font-size: 0.9rem;
  color: #ccc;
  margin: 0;
}

.rating {
  display: flex;
  gap: 5px;
}

.star {
  font-size: 1.5rem;
  color: #333;
  transition: color 0.3s ease;
}

.star.filled {
  color: #FFD700;
}

.testimonial-body {
  margin-bottom: 30px;
}

.testimonial-text {
  font-size: 1.3rem;
  line-height: 1.6;
  color: #f0f0f0;
  font-style: italic;
  margin: 0 0 20px 0;
  position: relative;
}

.product-mention {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  background: rgba(255, 107, 53, 0.1);
  border-radius: 15px;
  border: 1px solid rgba(255, 107, 53, 0.3);
}

.product-label {
  font-size: 0.9rem;
  color: #ccc;
  font-weight: 500;
}

.product-name {
  font-size: 1rem;
  color: #ff6b35;
  font-weight: 600;
}

.testimonial-footer {
  position: relative;
}

.quote-decoration {
  position: absolute;
  bottom: -20px;
  right: 20px;
}

.quote-mark {
  font-size: 6rem;
  color: rgba(255, 107, 53, 0.2);
  font-family: serif;
  line-height: 1;
}

.testimonials-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
}

.nav-dots {
  display: flex;
  gap: 15px;
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-dot.active {
  background: linear-gradient(90deg, #ff6b35, #2c5aa0);
  transform: scale(1.3);
}

.nav-controls {
  display: flex;
  gap: 15px;
}

.nav-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 107, 53, 0.5);
  transform: translateY(-2px);
}

.testimonials-stats {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-bottom: 80px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 30px 40px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
}

.stat-number {
  display: block;
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(90deg, #ff6b35, #2c5aa0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 1rem;
  color: #ccc;
  font-weight: 500;
  margin-bottom: 10px;
  display: block;
}

.stat-stars {
  display: flex;
  justify-content: center;
  gap: 3px;
}

.stat-stars .star {
  font-size: 1rem;
}

.trust-indicators {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.trust-badge {
  display: flex;
  align-items: center;
  gap: 20px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 25px 30px;
  transition: all 0.3s ease;
}

.trust-badge:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
}

.badge-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.badge-text span {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 5px;
}

.badge-text small {
  color: #ccc;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .testimonials-title {
    font-size: 2.5rem;
  }
  
  .testimonial-card {
    padding: 30px 25px;
  }
  
  .testimonial-header {
    flex-direction: column;
    gap: 20px;
    align-items: center;
    text-align: center;
  }
  
  .customer-info {
    flex-direction: column;
    text-align: center;
  }
  
  .testimonials-navigation {
    flex-direction: column;
    gap: 30px;
  }
  
  .testimonials-stats {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }
  
  .trust-indicators {
    grid-template-columns: 1fr;
  }
  
  .testimonial-text {
    font-size: 1.1rem;
  }
}
