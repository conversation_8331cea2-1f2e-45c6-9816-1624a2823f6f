import { useState } from 'react'
import Navigation from '../src/components/navigation.jsx'
import Header from '../src/components/header.jsx'
import Middle from '../src/components/middle.jsx'
import WatchGallery from '../src/components/WatchGallery.jsx'
import FeaturedCollections from '../src/components/FeaturedCollections.jsx'
import TechnicalSpecs from '../src/components/TechnicalSpecs.jsx'
import Heritage from '../src/components/Heritage.jsx'
import Testimonials from '../src/components/Testimonials.jsx'
import CallToAction from '../src/components/CallToAction.jsx'
import Footer from '../src/components/footer.jsx'
import DynamicBackground from '../src/components/DynamicBackground.jsx'
import '../src/App.css'
function App() {
  const [count, setCount] = useState(0)

  return (
    <div className='app-container'>
      <DynamicBackground />
      <Navigation />
      <div className='header-container'>
        <Header />
      </div>
      <div className='middle-container'>
        <Middle />
      </div>
      <div className='watch-gallery-container'>
        <WatchGallery />
      </div>
      <div className='featured-collections-container'>
        <FeaturedCollections />
      </div>
      <div className='technical-specs-container'>
        <TechnicalSpecs />
      </div>
      <div className='heritage-container'>
        <Heritage />
      </div>
      <div className='testimonials-container'>
        <Testimonials />
      </div>
      <div className='cta-container'>
        <CallToAction />
      </div>
      <div className='footer-container'>
        <Footer />
      </div>
    </div>
  )
}

export default App
