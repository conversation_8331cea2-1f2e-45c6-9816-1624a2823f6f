.footer {
  background: #232738;
  color: #c2c2c3;
  padding: 80px 0 0 0;
  position: relative;
  overflow: hidden;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
  gap: 60px;
  margin-bottom: 60px;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.brand-section {
  max-width: 350px;
}

.footer-logo {
  margin-bottom: 25px;
}

.logo-text {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(90deg, #ff6b35, #2c5aa0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 5px;
}

.logo-subtitle {
  font-size: 1.2rem;
  color: #ccc;
  font-weight: 300;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.brand-description {
  color: #ccc;
  line-height: 1.6;
  margin-bottom: 30px;
  font-size: 1rem;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-link {
  width: 45px;
  height: 45px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ccc;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-link:hover {
  background: rgba(255, 107, 53, 0.2);
  border-color: rgba(255, 107, 53, 0.5);
  color: #ff6b35;
  transform: translateY(-3px);
}

.footer-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 25px;
  position: relative;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #ff6b35, #2c5aa0);
  border-radius: 1px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-links a {
  color: #ccc;
  text-decoration: none;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  position: relative;
  padding-left: 0;
}

.footer-links a:hover {
  color: #ff6b35;
  padding-left: 10px;
}

.footer-links a::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 1px;
  background: #ff6b35;
  transition: width 0.3s ease;
}

.footer-links a:hover::before {
  width: 6px;
}

.contact-section {
  max-width: 280px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.contact-icon {
  font-size: 1.2rem;
  width: 35px;
  height: 35px;
  background: rgba(255, 107, 53, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 107, 53, 0.3);
  flex-shrink: 0;
}

.contact-text {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.contact-text span:first-child {
  color: white;
  font-weight: 600;
  font-size: 0.95rem;
}

.contact-text span:last-child {
  color: #ccc;
  font-size: 0.85rem;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 30px 0;
  background: rgba(0, 0, 0, 0.2);
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.copyright {
  color: #ccc;
  font-size: 0.9rem;
}

.footer-bottom-links {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.footer-bottom-links a {
  color: #ccc;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: #ff6b35;
}

.payment-methods {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #ccc;
  font-size: 0.9rem;
}

.payment-icons {
  display: flex;
  gap: 10px;
}

.payment-icon {
  width: 35px;
  height: 25px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.payment-icon:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 107, 53, 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .footer-content {
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 40px;
  }
  
  .footer-content .footer-section:nth-child(3),
  .footer-content .footer-section:nth-child(4) {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 60px 0 0 0;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .brand-section,
  .contact-section {
    max-width: none;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .footer-bottom-links {
    justify-content: center;
  }
  
  .payment-methods {
    justify-content: center;
  }
  
  .social-links {
    justify-content: center;
  }
  
  .contact-item {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-content {
    gap: 30px;
  }
  
  .footer-bottom-links {
    flex-direction: column;
    gap: 15px;
  }
  
  .logo-text {
    font-size: 2rem;
  }
  
  .social-links {
    gap: 10px;
  }
  
  .social-link {
    width: 40px;
    height: 40px;
  }
}
