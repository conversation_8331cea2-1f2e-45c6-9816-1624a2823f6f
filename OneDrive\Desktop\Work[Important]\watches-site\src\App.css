

@font-face {
  font-family: 'Boldivia';
  src: url('../src/assets/fonts/boldovia.regular.ttf') format('truetype');
  font-style: normal;
  font-weight: normal;
}


/* Ensure the app container takes full height */
#root {
  min-height: 100vh;
  min-width: 100vh;
}

.header-container{
  position: relative;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /* Add a gradient background to showcase the glassmorphism effect */
  background: linear-gradient(to right, #c2c2c3 50%, #232738 50%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

.middle-container{
  position: relative;
}

.featured-collections-container{
  position: relative;
}

.technical-specs-container{
  position: relative;
}

.heritage-container{
  position: relative;
}

.testimonials-container{
  position: relative;
}

.cta-container{
  position: relative;
}

.footer-container{
  position: static;
}