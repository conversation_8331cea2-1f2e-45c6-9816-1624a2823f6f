import React, { useState } from 'react';
import '../styles/TechnicalSpecs.css';

// Import watch image for technical display
import techWatch from '../assets/images/watch-3.png';

const TechnicalSpecs = () => {
  const [activeSpec, setActiveSpec] = useState(0);

  const specifications = [
    {
      category: "Movement",
      icon: "⚙️",
      details: [
        { label: "Type", value: "Swiss Automatic" },
        { label: "Caliber", value: "ETA 2824-2" },
        { label: "Jewels", value: "25 Jewels" },
        { label: "Power Reserve", value: "38 Hours" },
        { label: "Frequency", value: "28,800 vph" }
      ]
    },
    {
      category: "Case",
      icon: "🔧",
      details: [
        { label: "Material", value: "Titanium Grade 2" },
        { label: "Diameter", value: "42mm" },
        { label: "Thickness", value: "12.5mm" },
        { label: "Crystal", value: "Sapphire AR Coated" },
        { label: "Water Resistance", value: "100m / 330ft" }
      ]
    },
    {
      category: "Design",
      icon: "🎨",
      details: [
        { label: "Dial", value: "Matte Black" },
        { label: "Hands", value: "Luminous" },
        { label: "Markers", value: "Applied Indices" },
        { label: "Bezel", value: "Unidirectional" },
        { label: "Crown", value: "Screw-down" }
      ]
    },
    {
      category: "Strap",
      icon: "📏",
      details: [
        { label: "Material", value: "Italian Leather" },
        { label: "Color", value: "Black" },
        { label: "Width", value: "22mm" },
        { label: "Buckle", value: "Titanium Deployant" },
        { label: "Length", value: "120/80mm" }
      ]
    }
  ];

  return (
    <section className="technical-specs">
      <div className="specs-container">
        <div className="specs-header">
          <h2 className="specs-title">Technical Excellence</h2>
          <p className="specs-subtitle">Precision engineering meets automotive innovation</p>
        </div>

        <div className="specs-content">
          <div className="specs-navigation">
            {specifications.map((spec, index) => (
              <button
                key={index}
                className={`spec-nav-btn ${index === activeSpec ? 'active' : ''}`}
                onClick={() => setActiveSpec(index)}
              >
                <span className="spec-icon">{spec.icon}</span>
                <span className="spec-category">{spec.category}</span>
              </button>
            ))}
          </div>

          <div className="specs-display">
            <div className="specs-visual">
              <div className="watch-diagram">
                <div className="tech-watch-container">
                  <img
                    src={techWatch}
                    alt="Technical Watch"
                    className="tech-watch-image"
                  />
                  <div className="tech-overlay"></div>
                </div>
                
                {/* Animated callouts */}
                <div className={`callout callout-1 ${activeSpec === 0 ? 'active' : ''}`}>
                  <div className="callout-line"></div>
                  <div className="callout-text">Swiss Movement</div>
                </div>
                <div className={`callout callout-2 ${activeSpec === 1 ? 'active' : ''}`}>
                  <div className="callout-line"></div>
                  <div className="callout-text">Titanium Case</div>
                </div>
                <div className={`callout callout-3 ${activeSpec === 2 ? 'active' : ''}`}>
                  <div className="callout-line"></div>
                  <div className="callout-text">Sapphire Crystal</div>
                </div>
                <div className={`callout callout-4 ${activeSpec === 3 ? 'active' : ''}`}>
                  <div className="callout-line"></div>
                  <div className="callout-text">Premium Strap</div>
                </div>
              </div>
            </div>

            <div className="specs-details">
              <div className="spec-category-header">
                <span className="category-icon">{specifications[activeSpec].icon}</span>
                <h3 className="category-title">{specifications[activeSpec].category}</h3>
              </div>
              
              <div className="spec-list">
                {specifications[activeSpec].details.map((detail, index) => (
                  <div key={index} className="spec-item">
                    <span className="spec-label">{detail.label}</span>
                    <span className="spec-value">{detail.value}</span>
                  </div>
                ))}
              </div>

              <div className="spec-highlight">
                <div className="highlight-badge">
                  <span>Porsche Design</span>
                  <span>Certified</span>
                </div>
                <p className="highlight-text">
                  Every component meets the exacting standards of Porsche's legendary engineering excellence.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="specs-footer">
          <div className="certification-badges">
            <div className="badge">
              <div className="badge-icon">🏆</div>
              <div className="badge-text">
                <span>Swiss Made</span>
                <small>Certified Quality</small>
              </div>
            </div>
            <div className="badge">
              <div className="badge-icon">💎</div>
              <div className="badge-text">
                <span>Premium Materials</span>
                <small>Titanium & Sapphire</small>
              </div>
            </div>
            <div className="badge">
              <div className="badge-icon">⚡</div>
              <div className="badge-text">
                <span>Precision</span>
                <small>±10 sec/day</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TechnicalSpecs;
