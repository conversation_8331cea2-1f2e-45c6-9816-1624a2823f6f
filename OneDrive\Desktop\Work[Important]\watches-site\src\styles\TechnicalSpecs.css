.technical-specs {
  padding: 120px 0;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.technical-specs::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(44, 90, 160, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.specs-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.specs-header {
  text-align: center;
  margin-bottom: 80px;
}

.specs-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  background: linear-gradient(90deg, #ff6b35, #2c5aa0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.specs-subtitle {
  font-size: 1.2rem;
  color: #ccc;
  max-width: 600px;
  margin: 0 auto;
}

.specs-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 60px;
}

.specs-navigation {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.spec-nav-btn {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 20px 30px;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  min-width: 120px;
}

.spec-nav-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
}

.spec-nav-btn.active {
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.2), rgba(44, 90, 160, 0.2));
  border-color: rgba(255, 107, 53, 0.5);
  transform: translateY(-5px);
}

.spec-icon {
  font-size: 2rem;
}

.spec-category {
  font-weight: 600;
  font-size: 0.9rem;
}

.specs-display {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.specs-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.watch-diagram {
  position: relative;
  width: 350px;
  height: 350px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tech-watch-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tech-watch-image {
  width: 90%;
  height: 90%;
  object-fit: contain;
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
  transition: all 0.4s ease;
  animation: techFloat 6s ease-in-out infinite;
}

.tech-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 107, 53, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 3s ease-in-out infinite;
}

.watch-face {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
}

.hour-markers {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.marker {
  position: absolute;
  top: 5px;
  left: 50%;
  width: 2px;
  height: 20px;
  background: rgba(255, 255, 255, 0.6);
  transform-origin: 50% 125px;
  margin-left: -1px;
}

.marker:nth-child(3n) {
  width: 3px;
  height: 30px;
  background: rgba(255, 255, 255, 0.8);
  margin-left: -1.5px;
}

.watch-hands {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.hour-hand, .minute-hand, .second-hand {
  position: absolute;
  background: white;
  transform-origin: bottom center;
  left: 50%;
}

.hour-hand {
  width: 4px;
  height: 60px;
  margin-left: -2px;
  top: -60px;
  transform: rotate(45deg);
  border-radius: 2px;
}

.minute-hand {
  width: 2px;
  height: 80px;
  margin-left: -1px;
  top: -80px;
  transform: rotate(90deg);
  border-radius: 1px;
}

.second-hand {
  width: 1px;
  height: 90px;
  margin-left: -0.5px;
  top: -90px;
  background: #ff6b35;
  transform: rotate(180deg);
  animation: tick 60s linear infinite;
}

.center-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.crown {
  position: absolute;
  right: -8px;
  top: 50%;
  width: 16px;
  height: 20px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0 8px 8px 0;
  transform: translateY(-50%);
}

.callout {
  position: absolute;
  opacity: 0;
  transition: all 0.5s ease;
  pointer-events: none;
}

.callout.active {
  opacity: 1;
}

.callout-1 { top: 10%; right: -100px; }
.callout-2 { right: -100px; top: 50%; }
.callout-3 { top: 10%; left: -100px; }
.callout-4 { bottom: 10%; left: -100px; }

.callout-line {
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #ff6b35, #2c5aa0);
  margin-bottom: 10px;
}

.callout-text {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  white-space: nowrap;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.specs-details {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 40px;
}

.spec-category-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
}

.category-icon {
  font-size: 2.5rem;
}

.category-title {
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.spec-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.spec-label {
  color: #ccc;
  font-weight: 500;
}

.spec-value {
  color: white;
  font-weight: 600;
  text-align: right;
}

.spec-highlight {
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(44, 90, 160, 0.1));
  border: 1px solid rgba(255, 107, 53, 0.3);
  border-radius: 15px;
  padding: 20px;
}

.highlight-badge {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.highlight-badge span {
  background: linear-gradient(90deg, #ff6b35, #2c5aa0);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.highlight-text {
  color: #ccc;
  line-height: 1.5;
  margin: 0;
}

.specs-footer {
  margin-top: 80px;
}

.certification-badges {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.badge {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px 30px;
  transition: all 0.3s ease;
}

.badge:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
}

.badge-icon {
  font-size: 2rem;
}

.badge-text span {
  display: block;
  font-weight: 600;
  color: white;
}

.badge-text small {
  color: #ccc;
  font-size: 0.85rem;
}

@keyframes tick {
  from { transform: rotate(180deg); }
  to { transform: rotate(540deg); }
}

@keyframes techFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-8px) rotate(1deg); }
  50% { transform: translateY(-4px) rotate(0deg); }
  75% { transform: translateY(-12px) rotate(-1deg); }
}

@media (max-width: 1024px) {
  .specs-display {
    grid-template-columns: 1fr;
    gap: 60px;
  }
  
  .callout {
    display: none;
  }
}

@media (max-width: 768px) {
  .specs-navigation {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }

  .watch-diagram {
    width: 250px;
    height: 250px;
  }

  .specs-details {
    padding: 30px 20px;
  }

  .certification-badges {
    flex-direction: column;
    align-items: center;
  }
}
