.call-to-action {
  padding: 120px 0;
  background: #232738;
  position: relative;
  overflow: hidden;
}

.cta-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.cta-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  margin-bottom: 80px;
}

.cta-main {
  display: flex;
  flex-direction: column;
  gap: 50px;
}

.cta-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #c2c2c3;
  line-height: 1.1;
  margin-bottom: 20px;
  letter-spacing: -0.02em;
  text-transform: uppercase;
}

.cta-subtitle {
  font-size: 1.2rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.cta-features {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 1.1rem;
  color: #333;
  font-weight: 500;
}

.feature-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(44, 90, 160, 0.1));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 107, 53, 0.2);
}

.cta-actions {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.primary-actions {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.cta-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 18px 32px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  text-decoration: none;
  min-width: 200px;
  justify-content: center;
}

.cta-btn.primary {
  background: linear-gradient(90deg, #ff6b35, #2c5aa0);
  color: white;
  box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
}

.cta-btn.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(255, 107, 53, 0.4);
}

.cta-btn.secondary {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  color: #1a1a1a;
  border: 2px solid rgba(44, 90, 160, 0.3);
}

.cta-btn.secondary:hover {
  background: rgba(44, 90, 160, 0.1);
  border-color: rgba(44, 90, 160, 0.5);
  transform: translateY(-3px);
}

.newsletter-signup {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.newsletter-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 10px;
}

.newsletter-subtitle {
  color: #666;
  margin-bottom: 20px;
  font-size: 1rem;
}

.newsletter-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.input-group {
  display: flex;
  gap: 10px;
}

.email-input {
  flex: 1;
  padding: 15px 20px;
  border: 2px solid rgba(44, 90, 160, 0.2);
  border-radius: 25px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.email-input:focus {
  outline: none;
  border-color: #ff6b35;
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.subscribe-btn {
  padding: 15px 25px;
  background: linear-gradient(90deg, #ff6b35, #2c5aa0);
  color: white;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.subscribe-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
}

.success-message {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  padding: 12px 20px;
  border-radius: 15px;
  border: 1px solid rgba(76, 175, 80, 0.3);
  font-weight: 500;
  text-align: center;
}

.cta-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.watch-showcase {
  position: relative;
  width: 400px;
  height: 400px;
}

.showcase-watch {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.watch-glow {
  position: absolute;
  top: -30px;
  left: -30px;
  right: -30px;
  bottom: -30px;
  background: radial-gradient(circle, rgba(255, 107, 53, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 3s ease-in-out infinite;
  z-index: 1;
}

.showcase-watch-image {
  width: 80%;
  height: 80%;
  object-fit: contain;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
  animation: float 6s ease-in-out infinite;
  transition: all 0.4s ease;
}

.showcase-watch:hover .showcase-watch-image {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.4));
}



.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  font-size: 2rem;
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.element-1 {
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.element-2 {
  top: 20%;
  left: 10%;
  animation-delay: 1.5s;
}

.element-3 {
  bottom: 20%;
  right: 15%;
  animation-delay: 3s;
}

.element-4 {
  bottom: 10%;
  left: 15%;
  animation-delay: 4.5s;
}

.cta-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(90deg, #ff6b35, #2c5aa0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
}

.stat-label {
  color: #666;
  font-weight: 500;
  font-size: 1rem;
}

.urgency-banner {
  background: linear-gradient(90deg, #ff6b35, #2c5aa0);
  border-radius: 20px;
  padding: 25px 30px;
  box-shadow: 0 10px 40px rgba(255, 107, 53, 0.3);
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  color: white;
  text-align: center;
  flex-wrap: wrap;
}

.banner-icon {
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

.banner-text {
  font-size: 1.1rem;
  font-weight: 500;
}

.banner-timer {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 15px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.1); }
}

@keyframes tick {
  from { transform: rotate(180deg); }
  to { transform: rotate(540deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

@media (max-width: 1024px) {
  .cta-content {
    grid-template-columns: 1fr;
    gap: 60px;
  }
  
  .watch-showcase {
    width: 300px;
    height: 300px;
  }
  
  .showcase-watch {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 768px) {
  .cta-title {
    font-size: 2.5rem;
  }
  
  .primary-actions {
    flex-direction: column;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .cta-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .banner-content {
    flex-direction: column;
    gap: 15px;
  }
}
